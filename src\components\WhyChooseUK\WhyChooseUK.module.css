/* Why Choose UK Section */
.whyChooseSection {
  position: relative;
  padding: 6rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 35%, #334155 70%, #475569 100%);
  overflow: hidden;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}

/* Content Wrapper */
.contentWrapper {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  align-items: center;
}

@media (min-width: 1024px) {
  .contentWrapper {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

/* Left Content */
.leftContent {
  animation: fadeInLeft 0.8s ease-out;
}

/* Title Section */
.titleSection {
  margin-bottom: 2rem;
}

.sectionTitle {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0;
}

.titleWhy {
  font-size: 2.5rem;
  font-weight: 700;
  color: #e2e8f0;
  line-height: 1.1;
}

.titleUK {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #60a5fa, #3b82f6, #2563eb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.1;
}

@media (min-width: 768px) {
  .titleWhy { font-size: 3rem; }
  .titleUK { font-size: 4rem; }
}

@media (min-width: 1024px) {
  .titleWhy { font-size: 3.5rem; }
  .titleUK { font-size: 4.5rem; }
}

/* Description */
.description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #cbd5e1;
  margin-bottom: 3rem;
  max-width: 600px;
}

@media (min-width: 1024px) {
  .description {
    font-size: 1.2rem;
  }
}

/* Features Grid */
.featuresGrid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.featureItem {
  display: flex;
  gap: 1rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.featureItem:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(255, 255, 255, 0.08);
}

.featureIcon {
  flex-shrink: 0;
  width: 3.5rem;
  height: 3.5rem;
  background: linear-gradient(135deg, #60a5fa, #3b82f6, #2563eb);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.featureContent {
  flex: 1;
}

.featureTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.featureDescription {
  font-size: 0.95rem;
  color: #cbd5e1;
  line-height: 1.5;
  margin: 0;
}

/* Right Content */
.rightContent {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInRight 0.8s ease-out 0.3s both;
}

/* Image Container */
.imageContainer {
  position: relative;
  width: 100%;
  max-width: 650px;
  aspect-ratio: 1;
  border-radius: 30px;
  overflow: hidden;
  background: linear-gradient(135deg, #60a5fa, #3b82f6, #2563eb);
  padding: 4rem;
  box-shadow: 0 40px 80px rgba(59, 130, 246, 0.5), 0 0 60px rgba(59, 130, 246, 0.3);
  border: 3px solid rgba(255, 255, 255, 0.15);
}

.ukImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: brightness(1.2) contrast(1.2) saturate(1.1);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  drop-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.imageContainer:hover .ukImage {
  transform: scale(1.08) rotate(2deg);
}

.imageOverlay {
  position: absolute;
  bottom: 1.5rem;
  left: 1.5rem;
  right: 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 15px;
  padding: 1.25rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.overlayContent {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.overlayIcon {
  font-size: 1.5rem;
}

.overlayText {
  font-weight: 600;
  color: #1e293b;
}

/* Floating Stats */
.floatingStats {
  position: absolute;
  top: -1rem;
  right: -1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1.25rem;
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  text-align: center;
  min-width: 120px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  animation: float 3s ease-in-out infinite;
}

.statCard:nth-child(2) {
  animation-delay: 1.5s;
}

.statNumber {
  display: block;
  font-size: 1.5rem;
  font-weight: 800;
  color: #3b82f6;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.statLabel {
  display: block;
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  line-height: 1.2;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.bgShape1,
.bgShape2,
.bgShape3 {
  position: absolute;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.1);
  animation: floatSlow 8s ease-in-out infinite;
}

.bgShape1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: -5%;
  animation-delay: 0s;
}

.bgShape2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: -3%;
  animation-delay: 3s;
}

.bgShape3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 15%;
  animation-delay: 6s;
}

/* Animations */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes floatSlow {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

/* Mobile Adjustments */
@media (max-width: 768px) {
  .whyChooseSection {
    padding: 3rem 0;
  }

  .floatingStats {
    position: static;
    flex-direction: row;
    justify-content: center;
    margin-top: 2rem;
  }

  .imageContainer {
    max-width: 450px;
  }
}
