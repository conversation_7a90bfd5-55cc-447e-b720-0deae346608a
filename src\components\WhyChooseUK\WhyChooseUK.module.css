/* Why Choose UK Section */
.whyChooseSection {
  position: relative;
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  overflow: hidden;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}

/* Content Wrapper */
.contentWrapper {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  align-items: center;
}

@media (min-width: 1024px) {
  .contentWrapper {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

/* Left Content */
.leftContent {
  animation: fadeInLeft 0.8s ease-out;
}

/* Title Section */
.titleSection {
  margin-bottom: 2rem;
}

.sectionTitle {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0;
}

.titleWhy {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.1;
}

.titleUK {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.1;
}

@media (min-width: 768px) {
  .titleWhy { font-size: 3rem; }
  .titleUK { font-size: 4rem; }
}

@media (min-width: 1024px) {
  .titleWhy { font-size: 3.5rem; }
  .titleUK { font-size: 4.5rem; }
}

/* Description */
.description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #475569;
  margin-bottom: 3rem;
  max-width: 600px;
}

@media (min-width: 1024px) {
  .description {
    font-size: 1.2rem;
  }
}

/* Features Grid */
.featuresGrid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.featureItem {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.featureItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: rgba(59, 130, 246, 0.3);
}

.featureIcon {
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.featureContent {
  flex: 1;
}

.featureTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.featureDescription {
  font-size: 0.95rem;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
}

/* Right Content */
.rightContent {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInRight 0.8s ease-out 0.3s both;
}

/* Image Container */
.imageContainer {
  position: relative;
  width: 100%;
  max-width: 400px;
  aspect-ratio: 1;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  padding: 2rem;
  box-shadow: 0 25px 50px rgba(59, 130, 246, 0.3);
}

.ukImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: brightness(1.1) contrast(1.1);
  transition: transform 0.3s ease;
}

.imageContainer:hover .ukImage {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 1rem;
}

.overlayContent {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.overlayIcon {
  font-size: 1.5rem;
}

.overlayText {
  font-weight: 600;
  color: #1e293b;
}

/* Floating Stats */
.floatingStats {
  position: absolute;
  top: -1rem;
  right: -1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.statCard {
  background: white;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 100px;
  border: 2px solid rgba(59, 130, 246, 0.1);
  animation: float 3s ease-in-out infinite;
}

.statCard:nth-child(2) {
  animation-delay: 1.5s;
}

.statNumber {
  display: block;
  font-size: 1.5rem;
  font-weight: 800;
  color: #3b82f6;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.statLabel {
  display: block;
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  line-height: 1.2;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.bgShape1,
.bgShape2,
.bgShape3 {
  position: absolute;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.05);
  animation: floatSlow 8s ease-in-out infinite;
}

.bgShape1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: -5%;
  animation-delay: 0s;
}

.bgShape2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: -3%;
  animation-delay: 3s;
}

.bgShape3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 15%;
  animation-delay: 6s;
}

/* Animations */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes floatSlow {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

/* Mobile Adjustments */
@media (max-width: 768px) {
  .whyChooseSection {
    padding: 3rem 0;
  }
  
  .floatingStats {
    position: static;
    flex-direction: row;
    justify-content: center;
    margin-top: 2rem;
  }
  
  .imageContainer {
    max-width: 300px;
  }
}
