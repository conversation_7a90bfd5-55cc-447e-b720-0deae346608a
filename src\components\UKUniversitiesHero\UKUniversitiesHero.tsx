'use client';

import styles from './UKUniversitiesHero.module.css';

export default function UKUniversitiesHero() {
  return (
    <div className={styles.ukHeroSection}>
      {/* Background Elements */}
      <div className={styles.heroBgElements}>
        <div className={styles.bgElement1}></div>
        <div className={styles.bgElement2}></div>
        <div className={styles.bgElement3}></div>
      </div>
      
      {/* Gradient Overlay */}
      <div className={styles.heroGradientOverlay}></div>
      
      {/* Union Jack Pattern */}
      <div className={styles.unionJackPattern}></div>

      <div className={styles.container}>
        <div className={styles.heroContent}>
          {/* Trust Badge */}
          <div className={styles.trustBadge}>
            <span className={styles.trustIcon}>🎓</span>
            <span className={styles.trustText}>Trusted by 1000+ Students</span>
          </div>

          {/* Main Heading */}
          <h1 className={styles.heroTitle}>
            <span className={styles.titleMain}>UK Universities</span>
            <span className={styles.titleHighlight}>40+ Partner Universities</span>
            <span className={styles.titleLocation}>Across UK</span>
            <span className={styles.titleEducation}>World-Class Education</span>
          </h1>

          {/* Description */}
          <p className={styles.heroDescription}>
            Discover your path to academic excellence in the United Kingdom. From prestigious Russell Group universities to innovative modern institutions, we'll help you find the perfect match for your academic journey.
          </p>

          {/* CTA Buttons */}
          <div className={styles.ctaContainer}>
            <a href="#universities" className={styles.primaryBtn}>
              <span>Explore Universities</span>
              <i className="fa fa-arrow-right"></i>
            </a>
            <a href="/contact" className={styles.secondaryBtn}>
              <span>Free Consultation</span>
              <i className="fa fa-phone"></i>
            </a>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className={styles.scrollIndicator}>
        <div className={styles.scrollMouse}>
          <div className={styles.scrollWheel}></div>
        </div>
        <span className={styles.scrollText}>Scroll to explore</span>
      </div>
    </div>
  );
}
