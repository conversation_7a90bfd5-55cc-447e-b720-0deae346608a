'use client';

import styles from './UKUniversitiesHero.module.css';

export default function UKUniversitiesHero() {
  return (
    <div className={styles.ukHeroSection}>
      {/* Background Elements */}
      <div className={styles.heroBgElements}>
        <div className={styles.bgElement1}></div>
        <div className={styles.bgElement2}></div>
        <div className={styles.bgElement3}></div>
      </div>
      
      {/* Gradient Overlay */}
      <div className={styles.heroGradientOverlay}></div>
      
      {/* Union Jack Pattern */}
      <div className={styles.unionJackPattern}></div>

      <div className={styles.container}>
        <div className={styles.heroContent}>
          {/* Trust Badge */}
          <div className={styles.trustBadge}>
            <span className={styles.trustIcon}>🎓</span>
            <span className={styles.trustText}>Trusted by 1000+ Students</span>
          </div>

          {/* Main Heading */}
          <h1 className={styles.heroTitle}>
            <span className={styles.titleMain}>UK Universities</span>
            <span className={styles.titleHighlight}>40+ Partner Universities</span>
            <span className={styles.titleLocation}>Across UK</span>
            <span className={styles.titleEducation}>World-Class Education</span>
          </h1>

          {/* Description */}
          <p className={styles.heroDescription}>
            Discover your path to academic excellence in the United Kingdom. From prestigious Russell Group universities to innovative modern institutions, we'll help you find the perfect match for your academic journey.
          </p>

          {/* CTA Buttons */}
          <div className={styles.ctaContainer}>
            <a href="#universities" className={styles.primaryBtn}>
              <span>Explore Universities</span>
              <i className="fa fa-arrow-right"></i>
            </a>
            <a href="/contact" className={styles.secondaryBtn}>
              <span>Free Consultation</span>
              <i className="fa fa-phone"></i>
            </a>
          </div>

          {/* Stats */}
          <div className={styles.statsContainer}>
            <div className={styles.statItem}>
              <span className={styles.statNumber}>40+</span>
              <span className={styles.statLabel}>Partner Universities</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statNumber}>95%</span>
              <span className={styles.statLabel}>Success Rate</span>
            </div>
            <div className={styles.statItem}>
              <span className={styles.statNumber}>1000+</span>
              <span className={styles.statLabel}>Students Placed</span>
            </div>
          </div>
        </div>

        {/* Hero Image/Visual */}
        <div className={styles.heroVisual}>
          <div className={styles.imageContainer}>
            <img 
              src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
              alt="UK University Campus" 
              className={styles.heroImage}
            />
            <div className={styles.imageOverlay}>
              <div className={styles.overlayContent}>
                <span className={styles.overlayIcon}>🏛️</span>
                <span className={styles.overlayText}>Historic Excellence</span>
              </div>
            </div>
          </div>
          
          {/* Floating Elements */}
          <div className={styles.floatingElement1}>
            <span className={styles.floatingIcon}>🎯</span>
            <span className={styles.floatingText}>Targeted Guidance</span>
          </div>
          
          <div className={styles.floatingElement2}>
            <span className={styles.floatingIcon}>⭐</span>
            <span className={styles.floatingText}>Top Rankings</span>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className={styles.scrollIndicator}>
        <div className={styles.scrollMouse}>
          <div className={styles.scrollWheel}></div>
        </div>
        <span className={styles.scrollText}>Scroll to explore</span>
      </div>
    </div>
  );
}
