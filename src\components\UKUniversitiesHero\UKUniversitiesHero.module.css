/* UK Universities Hero Section */
.ukHeroSection {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 35%, #2b70fa 100%);
  overflow: hidden;
  padding-top: 4rem; /* Mobile navbar height */
}

@media (min-width: 1024px) {
  .ukHeroSection {
    padding-top: 5rem; /* Desktop navbar height */
  }
}

/* Background Elements */
.heroBgElements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bgElement1,
.bgElement2,
.bgElement3 {
  position: absolute;
  border-radius: 50%;
  background: rgba(43, 112, 250, 0.1);
  animation: float 6s ease-in-out infinite;
}

.bgElement1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: -10%;
  animation-delay: 0s;
}

.bgElement2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: -5%;
  animation-delay: 2s;
}

.bgElement3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Gradient Overlay */
.heroGradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(15, 20, 25, 0.8) 0%, rgba(43, 112, 250, 0.1) 100%);
  z-index: 2;
}

/* Union Jack Pattern */
.unionJackPattern {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 200px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 60 30'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M0 0h60v30H0z'/%3E%3Cpath d='M0 0l60 30M60 0L0 30'/%3E%3Cpath d='M30 0v30M0 15h60'/%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.5;
  z-index: 2;
}

/* Container */
.container {
  position: relative;
  z-index: 10;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: calc(100vh - 4rem);
  gap: 2rem;
}

@media (min-width: 1024px) {
  .container {
    min-height: calc(100vh - 5rem);
    padding: 0 2rem;
  }
}

/* Hero Content */
.heroContent {
  text-align: center;
  width: 100%;
}

/* Trust Badge */
.trustBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.5rem 1rem;
  animation: fadeInUp 0.8s ease-out;
  order: -1;
  align-self: center;
  margin-top: 1rem;
}

@media (min-width: 1024px) {
  .trustBadge {
    margin-top: 1.5rem;
  }
}

.trustIcon {
  font-size: 1.2rem;
}

.trustText {
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Hero Title */
.heroTitle {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.titleMain {
  font-size: 3rem;
  font-weight: 800;
  color: white;
  line-height: 1.1;
}

.titleHighlight {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.1;
}

.titleLocation {
  font-size: 2rem;
  font-weight: 600;
  color: #93c5fd;
  line-height: 1.1;
}

.titleEducation {
  font-size: 1.8rem;
  font-weight: 600;
  color: #e5e7eb;
  line-height: 1.1;
}

@media (min-width: 768px) {
  .titleMain { font-size: 4rem; }
  .titleHighlight { font-size: 3.5rem; }
  .titleLocation { font-size: 2.5rem; }
  .titleEducation { font-size: 2.2rem; }
}

@media (min-width: 1024px) {
  .titleMain { font-size: 4.5rem; }
  .titleHighlight { font-size: 4rem; }
  .titleLocation { font-size: 3rem; }
  .titleEducation { font-size: 2.5rem; }
}



/* CTA Container */
.ctaContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

@media (min-width: 640px) {
  .ctaContainer {
    flex-direction: row;
    justify-content: center;
    gap: 1.5rem;
  }
}

/* Buttons */
.primaryBtn,
.secondaryBtn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.primaryBtn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.primaryBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
}

.secondaryBtn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.secondaryBtn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}





/* Scroll Indicator */
.scrollIndicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  animation: fadeIn 1s ease-out 1.5s both;
}

.scrollMouse {
  width: 24px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  position: relative;
}

.scrollWheel {
  width: 4px;
  height: 8px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 2px;
  position: absolute;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  animation: scrollAnimation 2s ease-in-out infinite;
}

.scrollText {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  font-weight: 500;
}

@keyframes scrollAnimation {
  0% { transform: translateX(-50%) translateY(0); opacity: 1; }
  50% { transform: translateX(-50%) translateY(10px); opacity: 0.5; }
  100% { transform: translateX(-50%) translateY(0); opacity: 1; }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
